import sys
import os
import bpy
import bpy.props
import re

# Add the 'libs' folder to the Python path
libs_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "lib")
if libs_path not in sys.path:
    sys.path.append(libs_path)

# OpenAI client will be created when needed

from .utilities import *
bl_info = {
    "name": "GPT-4 Blender Assistant",
    "blender": (2, 82, 0),
    "category": "Object",
    "author": "<PERSON><PERSON><PERSON> (@gd3kr)",
    "version": (2, 0, 0),
    "location": "3D View > UI > GPT-4 Blender Assistant",
    "description": "Generate Blender Python code using OpenAI's GPT-4 to perform various tasks.",
    "warning": "",
    "wiki_url": "",
    "tracker_url": "",
}

system_prompt = """You are an assistant made for the purposes of helping the user with Blender, the 3D software. 
- Respond with your answers in markdown (```). 
- Preferably import entire modules instead of bits. 
- Do not perform destructive operations on the meshes. 
- Do not use cap_ends. Do not do more than what is asked (setting up render settings, adding cameras, etc)
- Do not respond with anything that is not Python code.

Example:

user: create 10 cubes in random locations from -10 to 10
assistant:
```
import bpy
import random
bpy.ops.mesh.primitive_cube_add()

#how many cubes you want to add
count = 10

for c in range(0,count):
    x = random.randint(-10,10)
    y = random.randint(-10,10)
    z = random.randint(-10,10)
    bpy.ops.mesh.primitive_cube_add(location=(x,y,z))
```"""



class GPT4_OT_DeleteMessage(bpy.types.Operator):
    bl_idname = "gpt4.delete_message"
    bl_label = "Delete Message"
    bl_options = {'REGISTER', 'UNDO'}

    message_index: bpy.props.IntProperty()

    def execute(self, context):
        context.scene.gpt4_chat_history.remove(self.message_index)
        return {'FINISHED'}

class GPT4_OT_ShowCode(bpy.types.Operator):
    bl_idname = "gpt4.show_code"
    bl_label = "Show Code"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Code",
        description="The generated code",
        default="",
    )

    def execute(self, context):
        text_name = "GPT4_Generated_Code.py"
        text = bpy.data.texts.get(text_name)
        if text is None:
            text = bpy.data.texts.new(text_name)

        text.clear()
        text.write(self.code)

        text_editor_area = None
        for area in context.screen.areas:
            if area.type == 'TEXT_EDITOR':
                text_editor_area = area
                break

        if text_editor_area is None:
            text_editor_area = split_area_to_text_editor(context)
        
        text_editor_area.spaces.active.text = text

        return {'FINISHED'}

class GPT4_PT_Panel(bpy.types.Panel):
    bl_label = "GPT-4 Blender Assistant"
    bl_idname = "GPT4_PT_Panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'GPT-4 Assistant'

    def draw(self, context):
        layout = self.layout
        column = layout.column(align=True)

        column.label(text="Chat history:")
        box = column.box()
        for index, message in enumerate(context.scene.gpt4_chat_history):
            if message.type == 'assistant':
                row = box.row()
                row.label(text="Assistant: ")
                show_code_op = row.operator("gpt4.show_code", text="Show Code")
                show_code_op.code = message.content
                delete_message_op = row.operator("gpt4.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index
            else:
                row = box.row()
                row.label(text=f"User: {message.content}")
                delete_message_op = row.operator("gpt4.delete_message", text="", icon="TRASH", emboss=False)
                delete_message_op.message_index = index

        column.separator()

        # Get addon preferences to check if custom model is being used
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        if addon_prefs.use_custom_model and addon_prefs.custom_model:
            column.label(text=f"Model: {addon_prefs.custom_model} (Custom)")
        else:
            column.label(text="GPT Model:")
            column.prop(context.scene, "gpt4_model", text="")

        column.label(text="Enter your message:")
        column.prop(context.scene, "gpt4_chat_input", text="")
        button_label = "Please wait...(this might take some time)" if context.scene.gpt4_button_pressed else "Execute"
        row = column.row(align=True)
        row.operator("gpt4.send_message", text=button_label)
        row.operator("gpt4.clear_chat", text="Clear Chat")

        # Export/Import buttons
        row = column.row(align=True)
        row.operator("gpt4.export_chat", text="Export Chat", icon="EXPORT")
        row.operator("gpt4.import_chat", text="Import Chat", icon="IMPORT")

        # Undo/Backup buttons
        row = column.row(align=True)
        row.operator("gpt4.undo_operation", text="Undo Last", icon="LOOP_BACK")
        row.operator("gpt4.show_backups", text="Show Backups", icon="FILE_BACKUP")
        row.operator("gpt4.cleanup_backups", text="Cleanup", icon="TRASH")

        column.separator()

class GPT4_OT_ClearChat(bpy.types.Operator):
    bl_idname = "gpt4.clear_chat"
    bl_label = "Clear Chat"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        context.scene.gpt4_chat_history.clear()
        # Auto-save empty chat history
        from .utilities import save_chat_history
        save_chat_history(context.scene.gpt4_chat_history)
        return {'FINISHED'}

class GPT4_OT_ExportChat(bpy.types.Operator):
    bl_idname = "gpt4.export_chat"
    bl_label = "Export Chat History"
    bl_options = {'REGISTER', 'UNDO'}

    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Choose a file path to export chat history",
        default="blendergpt_chat_export.json",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        try:
            import json
            history_data = []

            for message in context.scene.gpt4_chat_history:
                history_data.append({
                    "type": message.type,
                    "content": message.content
                })

            with open(self.filepath, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, indent=2, ensure_ascii=False)

            self.report({'INFO'}, f"Chat history exported to: {self.filepath}")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error exporting chat history: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class GPT4_OT_ImportChat(bpy.types.Operator):
    bl_idname = "gpt4.import_chat"
    bl_label = "Import Chat History"
    bl_options = {'REGISTER', 'UNDO'}

    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Choose a file path to import chat history",
        default="",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        try:
            import json

            with open(self.filepath, 'r', encoding='utf-8') as f:
                history_data = json.load(f)

            # Clear existing history
            context.scene.gpt4_chat_history.clear()

            # Load imported history
            for item in history_data:
                message = context.scene.gpt4_chat_history.add()
                message.type = item.get("type", "user")
                message.content = item.get("content", "")

            # Auto-save imported history
            from .utilities import save_chat_history
            save_chat_history(context.scene.gpt4_chat_history)

            self.report({'INFO'}, f"Chat history imported from: {self.filepath}")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error importing chat history: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class GPT4_OT_CodePreview(bpy.types.Operator):
    bl_idname = "gpt4.code_preview"
    bl_label = "Code Preview"
    bl_options = {'REGISTER', 'UNDO'}

    code: bpy.props.StringProperty(
        name="Generated Code",
        description="The generated Python code to preview",
        default=""
    )

    def execute(self, context):
        # Save scene state before executing code
        from .utilities import save_scene_state, cleanup_old_backups
        backup_path = save_scene_state()

        # Execute the code
        try:
            global_namespace = globals().copy()
            exec(self.code, global_namespace)
            self.report({'INFO'}, "Code executed successfully! (Backup saved for undo)")

            # Clean up old backups periodically
            cleanup_old_backups(10)

            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error executing code: {e}")
            return {'CANCELLED'}

    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self, width=600)

    def draw(self, context):
        layout = self.layout
        layout.label(text="Generated Code Preview:", icon="FILE_SCRIPT")

        # Create a scrollable text area
        box = layout.box()
        lines = self.code.split('\n')

        # Show first 20 lines with line numbers
        for i, line in enumerate(lines[:20]):
            row = box.row()
            row.alignment = 'LEFT'
            row.label(text=f"{i+1:3d}: {line}")

        if len(lines) > 20:
            box.label(text=f"... and {len(lines) - 20} more lines")

        layout.separator()
        layout.label(text="Do you want to execute this code?", icon="QUESTION")

class GPT4_OT_UndoOperation(bpy.types.Operator):
    bl_idname = "gpt4.undo_operation"
    bl_label = "Undo Last Operation"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            from .utilities import get_recent_backups
            backups = get_recent_backups(2)  # Get last 2 backups

            if len(backups) >= 2:
                # Load the previous backup (second most recent)
                backup_path = backups[1]
                bpy.ops.wm.open_mainfile(filepath=backup_path)
                self.report({'INFO'}, f"Undone to previous state: {os.path.basename(backup_path)}")
                return {'FINISHED'}
            else:
                self.report({'WARNING'}, "No previous state available for undo")
                return {'CANCELLED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error during undo: {e}")
            return {'CANCELLED'}

class GPT4_OT_ShowBackups(bpy.types.Operator):
    bl_idname = "gpt4.show_backups"
    bl_label = "Show Recent Backups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        from .utilities import get_recent_backups
        backups = get_recent_backups(10)

        if backups:
            self.report({'INFO'}, f"Found {len(backups)} recent backups")
            for i, backup in enumerate(backups):
                print(f"{i+1}. {os.path.basename(backup)} - {os.path.getmtime(backup)}")
        else:
            self.report({'INFO'}, "No backups found")

        return {'FINISHED'}

class GPT4_OT_CleanupBackups(bpy.types.Operator):
    bl_idname = "gpt4.cleanup_backups"
    bl_label = "Cleanup Old Backups"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            from .utilities import cleanup_old_backups
            cleanup_old_backups(5)  # Keep only 5 most recent
            self.report({'INFO'}, "Old backups cleaned up")
            return {'FINISHED'}
        except Exception as e:
            self.report({'ERROR'}, f"Error cleaning backups: {e}")
            return {'CANCELLED'}

class GPT4_OT_Execute(bpy.types.Operator):
    bl_idname = "gpt4.send_message"
    bl_label = "Send Message"
    bl_options = {'REGISTER', 'UNDO'}

    natural_language_input: bpy.props.StringProperty(
        name="Command",
        description="Enter the natural language command",
        default="",
    )

    # Background processing variables
    _timer = None
    _thread = None
    _result = None
    _error = None
    _processing = False

    def execute(self, context):
        # Check if already processing
        if self._processing:
            self.report({'WARNING'}, "Already processing a request. Please wait...")
            return {'CANCELLED'}

        # Get preferences
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        # Get API key from addon preferences or environment
        api_key = addon_prefs.api_key
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key detected. Please set the API key in the addon preferences.")
            return {'CANCELLED'}

        # Get base URL from preferences or environment
        base_url = addon_prefs.base_url
        if not base_url:
            base_url = os.getenv("OPENAI_BASE_URL")

        # Get model selection
        model = None
        if addon_prefs.use_custom_model and addon_prefs.custom_model:
            model = addon_prefs.custom_model

        # Store user input and add to chat history
        user_input = context.scene.gpt4_chat_input
        if not user_input.strip():
            self.report({'ERROR'}, "Please enter a message.")
            return {'CANCELLED'}

        message = context.scene.gpt4_chat_history.add()
        message.type = 'user'
        message.content = user_input

        # Auto-save chat history after adding user message
        from .utilities import save_chat_history
        save_chat_history(context.scene.gpt4_chat_history)

        # Clear the chat input field
        context.scene.gpt4_chat_input = ""

        # Set processing state
        context.scene.gpt4_button_pressed = True
        self._processing = True
        self._result = None
        self._error = None

        # Start background processing
        import threading
        self._thread = threading.Thread(
            target=self._background_process,
            args=(user_input, context.scene.gpt4_chat_history, context, api_key, base_url, model)
        )
        self._thread.daemon = True
        self._thread.start()

        # Start timer to check for completion
        wm = context.window_manager
        self._timer = wm.event_timer_add(0.1, window=context.window)
        wm.modal_handler_add(self)

        return {'RUNNING_MODAL'}

    def _background_process(self, user_input, chat_history, context, api_key, base_url, model):
        """Background thread function for API call"""
        try:
            # Get addon preferences for AI configuration
            preferences = context.preferences
            addon_prefs = preferences.addons[__name__].preferences

            result = generate_blender_code(
                user_input,
                chat_history,
                context,
                system_prompt,
                api_key,
                base_url,
                model,
                timeout=60,  # 60 second timeout
                temperature=addon_prefs.temperature,
                max_tokens=addon_prefs.max_tokens,
                top_p=addon_prefs.top_p
            )
            self._result = result
        except Exception as e:
            self._error = f"Background processing error: {str(e)}"

    def modal(self, context, event):
        """Modal handler to check background process completion"""
        if event.type == 'TIMER':
            # Check if thread is still alive
            if self._thread and self._thread.is_alive():
                return {'PASS_THROUGH'}

            # Thread completed, process results
            wm = context.window_manager
            wm.event_timer_remove(self._timer)
            self._timer = None

            context.scene.gpt4_button_pressed = False
            self._processing = False

            # Handle errors
            if self._error:
                self.report({'ERROR'}, self._error)
                return {'CANCELLED'}

            if not self._result:
                self.report({'ERROR'}, "No response received from API")
                return {'CANCELLED'}

            # Check for API errors
            if self._result.get('error'):
                error_msg = self._result['error']
                if 'timeout' in error_msg.lower():
                    self.report({'ERROR'}, "Request timed out. Please try again.")
                elif 'rate limit' in error_msg.lower():
                    self.report({'ERROR'}, "API rate limit exceeded. Please wait and try again.")
                elif 'connection' in error_msg.lower():
                    self.report({'ERROR'}, "Connection error. Please check your internet connection.")
                else:
                    self.report({'ERROR'}, f"API Error: {error_msg}")
                return {'CANCELLED'}

            # Process successful result
            blender_code = self._result.get('code')
            if blender_code and blender_code.strip():
                # Add assistant response to chat history
                message = context.scene.gpt4_chat_history.add()
                message.type = 'assistant'
                message.content = blender_code

                # Auto-save chat history after adding assistant response
                from .utilities import save_chat_history
                save_chat_history(context.scene.gpt4_chat_history)

                # Show code preview instead of direct execution
                bpy.ops.gpt4.code_preview('INVOKE_DEFAULT', code=blender_code)
            else:
                # More detailed error reporting
                error_details = self._result.get('error', 'Unknown error')
                self.report({'ERROR'}, f"No code generated from API response. Details: {error_details}")
                print(f"Full API result: {self._result}")  # Debug output
                return {'CANCELLED'}

            return {'FINISHED'}

        return {'PASS_THROUGH'}


class GPT4_OT_TestConnection(bpy.types.Operator):
    bl_idname = "gpt4.test_connection"
    bl_label = "Test API Connection"
    bl_options = {'REGISTER'}

    def execute(self, context):
        preferences = context.preferences
        addon_prefs = preferences.addons[__name__].preferences

        api_key = addon_prefs.api_key
        if not api_key:
            api_key = os.getenv("OPENAI_API_KEY")

        if not api_key:
            self.report({'ERROR'}, "No API key found")
            return {'CANCELLED'}

        base_url = addon_prefs.base_url
        if not base_url:
            base_url = os.getenv("OPENAI_BASE_URL")

        model = addon_prefs.custom_model if addon_prefs.use_custom_model else context.scene.gpt4_model

        from .utilities import test_openrouter_connection
        result = test_openrouter_connection(api_key, base_url, model)

        if result["success"]:
            self.report({'INFO'}, f"Connection successful! Response: {result['content'][:50]}...")
        else:
            self.report({'ERROR'}, f"Connection failed: {result['error']}")

        return {'FINISHED'}

def menu_func(self, context):
    self.layout.operator(GPT4_OT_Execute.bl_idname)

class GPT4AddonPreferences(bpy.types.AddonPreferences):
    bl_idname = __name__

    api_key: bpy.props.StringProperty(
        name="API Key",
        description="Enter your OpenAI API Key",
        default="",
        subtype="PASSWORD",
    )

    base_url: bpy.props.StringProperty(
        name="Base URL",
        description="OpenAI API Base URL (leave empty for default: https://api.openai.com/v1)",
        default="",
    )

    custom_model: bpy.props.StringProperty(
        name="Custom Model",
        description="Custom model ID (e.g., gpt-4-turbo, claude-3-opus, local-model)",
        default="",
    )

    use_custom_model: bpy.props.BoolProperty(
        name="Use Custom Model",
        description="Use custom model instead of predefined ones",
        default=False,
    )

    # AI Configuration Parameters
    temperature: bpy.props.FloatProperty(
        name="Temperature",
        description="Controls randomness in the output (0.0 = deterministic, 2.0 = very random)",
        default=0.7,
        min=0.0,
        max=2.0,
        step=0.1,
        precision=1,
    )

    max_tokens: bpy.props.IntProperty(
        name="Max Tokens",
        description="Maximum number of tokens to generate in the response",
        default=1500,
        min=1,
        max=4000,
    )

    top_p: bpy.props.FloatProperty(
        name="Top P",
        description="Controls diversity via nucleus sampling (0.1 = only top 10% of tokens)",
        default=1.0,
        min=0.0,
        max=1.0,
        step=0.1,
        precision=2,
    )

    def draw(self, context):
        layout = self.layout
        layout.prop(self, "api_key")
        layout.separator()
        layout.prop(self, "base_url")
        layout.separator()
        layout.prop(self, "use_custom_model")
        if self.use_custom_model:
            layout.prop(self, "custom_model")
        layout.separator()

        # AI Configuration Section
        box = layout.box()
        box.label(text="AI Configuration:", icon="SETTINGS")
        box.prop(self, "temperature")
        box.prop(self, "max_tokens")
        box.prop(self, "top_p")

        layout.separator()
        layout.operator("gpt4.test_connection", text="Test API Connection")

def register():
    bpy.utils.register_class(GPT4AddonPreferences)
    bpy.utils.register_class(GPT4_OT_Execute)
    bpy.utils.register_class(GPT4_PT_Panel)
    bpy.utils.register_class(GPT4_OT_ClearChat)
    bpy.utils.register_class(GPT4_OT_ExportChat)
    bpy.utils.register_class(GPT4_OT_ImportChat)
    bpy.utils.register_class(GPT4_OT_CodePreview)
    bpy.utils.register_class(GPT4_OT_UndoOperation)
    bpy.utils.register_class(GPT4_OT_ShowBackups)
    bpy.utils.register_class(GPT4_OT_CleanupBackups)
    bpy.utils.register_class(GPT4_OT_ShowCode)
    bpy.utils.register_class(GPT4_OT_DeleteMessage)
    bpy.utils.register_class(GPT4_OT_TestConnection)


    bpy.types.VIEW3D_MT_mesh_add.append(menu_func)
    init_props()

    # Load saved chat history on addon startup
    def load_history_delayed():
        """Load chat history with a small delay to ensure context is ready"""
        try:
            if bpy.context and bpy.context.scene:
                from .utilities import load_chat_history
                load_chat_history(bpy.context)
        except Exception as e:
            print(f"Error loading chat history on startup: {e}")

    # Use a timer to delay loading slightly
    bpy.app.timers.register(load_history_delayed, first_interval=0.1)


def unregister():
    bpy.utils.unregister_class(GPT4AddonPreferences)
    bpy.utils.unregister_class(GPT4_OT_Execute)
    bpy.utils.unregister_class(GPT4_PT_Panel)
    bpy.utils.unregister_class(GPT4_OT_ClearChat)
    bpy.utils.unregister_class(GPT4_OT_ExportChat)
    bpy.utils.unregister_class(GPT4_OT_ImportChat)
    bpy.utils.unregister_class(GPT4_OT_CodePreview)
    bpy.utils.unregister_class(GPT4_OT_UndoOperation)
    bpy.utils.unregister_class(GPT4_OT_ShowBackups)
    bpy.utils.unregister_class(GPT4_OT_CleanupBackups)
    bpy.utils.unregister_class(GPT4_OT_ShowCode)
    bpy.utils.unregister_class(GPT4_OT_DeleteMessage)
    bpy.utils.unregister_class(GPT4_OT_TestConnection)

    bpy.types.VIEW3D_MT_mesh_add.remove(menu_func)
    clear_props()


if __name__ == "__main__":
    register()
